import pandas as pd
import numpy as np
from sklearn.model_selection import KFold
from sklearn.metrics import root_mean_squared_error
import lightgbm as lgb
import xgboost as xgb
import catboost as cb
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import r2_score, mean_absolute_error

# Load datasets
train=pd.read_csv('Train.csv')
test=pd.read_csv('Test.csv')
submission=pd.read_csv('Submission.csv')

# Split the data
X=train.drop(columns=['Job_Automation_Risk'])
y=train['Job_Automation_Risk']
X_test=test.drop(columns=['Job_Automation_Risk'], errors='ignore')
kf=KFold(n_splits=5, shuffle=True, random_state=42)

# Models
lgb_params={'objective': 'regression', 'metric': 'rmse', 'verbose': -1}
xgb_params={'objective': 'reg:squarederror', 'eval_metric': 'rmse', 'seed': 42}
cb_params={'loss_function': 'RMSE', 'verbose': 0, 'random_seed': 42}

lgb_preds=np.zeros(len(X_test))
xgb_preds=np.zeros(len(X_test))
cb_preds=np.zeros(len(X_test))

# variables for storing model scores
lgb_val_preds=[]
xgb_val_preds=[]
cb_val_preds=[]
y_val_true=[]

cv_rmse_scores=[]
lgb_cv_scores=[]
xgb_cv_scores=[]
cb_cv_scores=[]

for fold, (train_idx, valid_idx) in enumerate(kf.split(X)):
    print(f"Fold {fold+1}")
    X_train, X_valid=X.iloc[train_idx], X.iloc[valid_idx]
    y_train, y_valid=y.iloc[train_idx], y.iloc[valid_idx]
    
    # LightGBM
    lgb_train_data=lgb.Dataset(X_train, label=y_train)
    lgb_valid_data=lgb.Dataset(X_valid, label=y_valid)
    lgb_model=lgb.train(lgb_params, lgb_train_data, valid_sets=[lgb_valid_data], callbacks=[lgb.log_evaluation(0)])
    lgb_preds += lgb_model.predict(X_test) / kf.n_splits
    lgb_val_pred=lgb_model.predict(X_valid)

    # XGBoost
    xgb_model=xgb.XGBRegressor(**xgb_params)
    xgb_model.fit(X_train, y_train, eval_set=[(X_valid, y_valid)], verbose=False)
    xgb_preds+=xgb_model.predict(X_test) / kf.n_splits
    xgb_val_pred=xgb_model.predict(X_valid)

    # CatBoost
    cb_model=cb.CatBoostRegressor(**cb_params)
    cb_model.fit(X_train, y_train, eval_set=(X_valid, y_valid), verbose=False)
    cb_preds += cb_model.predict(X_test) / kf.n_splits
    cb_val_pred=cb_model.predict(X_valid)

    # Store validation predictions for analysis
    lgb_val_preds.extend(lgb_val_pred)
    xgb_val_preds.extend(xgb_val_pred)
    cb_val_preds.extend(cb_val_pred)
    y_val_true.extend(y_valid.values)

    # Individual model RMSE scores
    lgb_rmse=root_mean_squared_error(y_valid, lgb_val_pred)
    xgb_rmse=root_mean_squared_error(y_valid, xgb_val_pred)
    cb_rmse=root_mean_squared_error(y_valid, cb_val_pred)

    lgb_cv_scores.append(lgb_rmse)
    xgb_cv_scores.append(xgb_rmse)
    cb_cv_scores.append(cb_rmse)

    # RMSE for ensemble
    val_preds=(lgb_val_pred + xgb_val_pred + cb_val_pred) / 3
    rmse=root_mean_squared_error(y_valid, val_preds)
    cv_rmse_scores.append(rmse)

    print(f"  LightGBM RMSE: {lgb_rmse:.4f}")
    print(f"  XGBoost RMSE: {xgb_rmse:.4f}")
    print(f"  CatBoost RMSE: {cb_rmse:.4f}")
    print(f"  Ensemble RMSE: {rmse:.4f}")

# model comparison results
print("\n" + "="*50)
print("MODEL COMPARISON RESULTS")
print("="*50)
print(f"LightGBM Average CV RMSE: {np.mean(lgb_cv_scores):.4f} ± {np.std(lgb_cv_scores):.4f}")
print(f"XGBoost Average CV RMSE:  {np.mean(xgb_cv_scores):.4f} ± {np.std(xgb_cv_scores):.4f}")
print(f"CatBoost Average CV RMSE: {np.mean(cb_cv_scores):.4f} ± {np.std(cb_cv_scores):.4f}")
print(f"Ensemble Average CV RMSE: {np.mean(cv_rmse_scores):.4f} ± {np.std(cv_rmse_scores):.4f}")

# additional metrics
lgb_val_preds=np.array(lgb_val_preds)
xgb_val_preds=np.array(xgb_val_preds)
cb_val_preds=np.array(cb_val_preds)
y_val_true=np.array(y_val_true)

lgb_mae=mean_absolute_error(y_val_true, lgb_val_preds)
xgb_mae=mean_absolute_error(y_val_true, xgb_val_preds)
cb_mae=mean_absolute_error(y_val_true, cb_val_preds)

lgb_r2=r2_score(y_val_true, lgb_val_preds)
xgb_r2=r2_score(y_val_true, xgb_val_preds)
cb_r2=r2_score(y_val_true, cb_val_preds)

print(f"\nMAE Scores:")
print(f"LightGBM MAE: {lgb_mae:.4f}")
print(f"XGBoost MAE:  {xgb_mae:.4f}")
print(f"CatBoost MAE: {cb_mae:.4f}")

print(f"\nR² Scores:")
print(f"LightGBM R²: {lgb_r2:.4f}")
print(f"XGBoost R²:  {xgb_r2:.4f}")
print(f"CatBoost R²: {cb_r2:.4f}")

#Saving the predictions to submission.csv file
final_preds=(lgb_preds + xgb_preds + cb_preds) / 3
submission['Job_Automation_Risk']=final_preds
submission.to_csv('submission.csv', index=False)
print(f"\nSaved submission.csv with {len(final_preds)} predictions")


# visualization plots
print("\nCreating model comparison visualizations...")
plt.style.use('default')
fig, axes=plt.subplots(2, 2, figsize=(15, 12))
fig.suptitle('Model Performance Comparison', fontsize=16, fontweight='bold')

# 1. RMSE Comparison Bar Plot
ax1=axes[0, 0]
models=['LightGBM', 'XGBoost', 'CatBoost', 'Ensemble']
rmse_means=[np.mean(lgb_cv_scores), np.mean(xgb_cv_scores), np.mean(cb_cv_scores), np.mean(cv_rmse_scores)]
rmse_stds=[np.std(lgb_cv_scores), np.std(xgb_cv_scores), np.std(cb_cv_scores), np.std(cv_rmse_scores)]

colors=['lightblue', 'lightgreen', 'lightcoral', 'gold']
bars=ax1.bar(models, rmse_means, yerr=rmse_stds, capsize=5, color=colors, alpha=0.7, edgecolor='black')
ax1.set_title('Cross-Validation RMSE Comparison', fontweight='bold')
ax1.set_ylabel('RMSE')
ax1.grid(True, alpha=0.3)


for bar, mean_val, std_val in zip(bars, rmse_means, rmse_stds):
    height=bar.get_height()
    ax1.text(bar.get_x() + bar.get_width()/2., height + std_val + 0.001,
             f'{mean_val:.4f}', ha='center', va='bottom', fontweight='bold')

# 2. MAE Comparison Bar Plot
ax2=axes[0, 1]
mae_scores=[lgb_mae, xgb_mae, cb_mae]
bars2=ax2.bar(models[:3], mae_scores, color=colors[:3], alpha=0.7, edgecolor='black')
ax2.set_title('Mean Absolute Error Comparison', fontweight='bold')
ax2.set_ylabel('MAE')
ax2.grid(True, alpha=0.3)

for bar, mae_val in zip(bars2, mae_scores):
    height=bar.get_height()
    ax2.text(bar.get_x() + bar.get_width()/2., height + 0.001,
             f'{mae_val:.4f}', ha='center', va='bottom', fontweight='bold')

# 3. R² Score Comparison Bar Plot
ax3=axes[1, 0]
r2_scores=[lgb_r2, xgb_r2, cb_r2]
bars3=ax3.bar(models[:3], r2_scores, color=colors[:3], alpha=0.7, edgecolor='black')
ax3.set_title('R² Score Comparison', fontweight='bold')
ax3.set_ylabel('R² Score')
ax3.grid(True, alpha=0.3)

for bar, r2_val in zip(bars3, r2_scores):
    height=bar.get_height()
    ax3.text(bar.get_x() + bar.get_width()/2., height + 0.001,
             f'{r2_val:.4f}', ha='center', va='bottom', fontweight='bold')

# 4. Cross-Validation RMSE by Fold
ax4=axes[1, 1]
fold_numbers=list(range(1, len(lgb_cv_scores) + 1))
ax4.plot(fold_numbers, lgb_cv_scores, 'o-', label='LightGBM', color='blue', linewidth=2, markersize=6)
ax4.plot(fold_numbers, xgb_cv_scores, 's-', label='XGBoost', color='green', linewidth=2, markersize=6)
ax4.plot(fold_numbers, cb_cv_scores, '^-', label='CatBoost', color='red', linewidth=2, markersize=6)
ax4.plot(fold_numbers, cv_rmse_scores, 'd-', label='Ensemble', color='orange', linewidth=2, markersize=6)
ax4.set_title('RMSE by Cross-Validation Fold', fontweight='bold')
ax4.set_xlabel('Fold Number')
ax4.set_ylabel('RMSE')
ax4.legend()
ax4.grid(True, alpha=0.3)
ax4.set_xticks(fold_numbers)

plt.tight_layout()
plt.savefig('model_comparison.jpg', dpi=300, bbox_inches='tight')
print("Saved model comparison plot as 'model_comparison.jpg'")

# summary table
print("\n" + "="*60)
print("FINAL SUMMARY TABLE")
print("="*60)
print(f"{'Model':<12} {'RMSE':<10} {'MAE':<10} {'R²':<10}")
print("-" * 60)
print(f"{'LightGBM':<12} {np.mean(lgb_cv_scores):<10.4f} {lgb_mae:<10.4f} {lgb_r2:<10.4f}")
print(f"{'XGBoost':<12} {np.mean(xgb_cv_scores):<10.4f} {xgb_mae:<10.4f} {xgb_r2:<10.4f}")
print(f"{'CatBoost':<12} {np.mean(cb_cv_scores):<10.4f} {cb_mae:<10.4f} {cb_r2:<10.4f}")
print(f"{'Ensemble':<12} {np.mean(cv_rmse_scores):<10.4f} {'-':<10} {'-':<10}")
print("="*60)

